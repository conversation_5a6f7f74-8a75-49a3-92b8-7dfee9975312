import { test as base, expect } from '@playwright/test';
import path from 'path';
import fs from 'fs';

// Define a custom test that doesn't send Cloudflare headers to external services
const test = base.extend({
  context: async ({ browser }, use) => {
    // Create a context without the Cloudflare headers
    const context = await browser.newContext({
      extraHTTPHeaders: {} // Empty headers to override the default ones
    });
    await use(context);
  }
});

/**
 * E2E test for deleting emojis from chat messages
 * This test verifies that a user can delete emojis from a message before sending
 */
test.describe('Chat Delete Emoji', () => {
  test('should delete emojis from a message before sending', async ({ page }) => {
    console.log('Starting chat delete emoji test');
    
    // Navigate to the home page
    console.log('Navigating to home page');
    await page.goto('http://localhost:8080');
    await page.waitForLoadState('networkidle');
    
    // Take a screenshot of the home page
    await page.screenshot({ path: 'chat-delete-emoji-test-home-page.png' });
    console.log('Saved screenshot of home page');
    
    // Check if we're already logged in
    const userMenu = page.locator('a.navbar-link[href="/user-group"]:has-text("Organization")');
    const isLoggedIn = await userMenu.count() > 0;
    
    if (!isLoggedIn) {
      console.log('Not logged in, logging in now');
      
      // Click the login button
      await page.click('button.button.is-text:has-text("Login")');
      
      // Wait for the login form to appear
      await page.waitForSelector('input[name="username"]');
      
      // Take a screenshot of the login form
      await page.screenshot({ path: 'chat-delete-emoji-test-login-form.png' });
      console.log('Saved screenshot of login form');
      
      // Fill in login credentials
      console.log('Filling in login credentials');
      await page.fill('input[name="username"]', '<EMAIL>');
      await page.fill('input[name="password"]', '(abc123ABC)');
      
      // Click the login button
      console.log('Clicking submit button');
      await page.click('button[type="submit"]');
      
      // Short pause to allow for login processing
      console.log('Waiting briefly for login processing');
      await page.waitForTimeout(3000);
      
      // Wait for navigation to complete after login
      console.log('Waiting for navigation after login');
      await page.waitForLoadState('networkidle');
    } else {
      console.log('Already logged in, proceeding to chat');
    }
    
    // Navigate to the AI Chat page
    console.log('Navigating to AI Chat page');
    await page.goto('http://localhost:8080/ai-chat');
    await page.waitForLoadState('networkidle');
    
    // Take a screenshot of the AI Chat page
    await page.screenshot({ path: 'chat-delete-emoji-test-ai-chat-page.png' });
    console.log('Saved screenshot of AI Chat page');
    
    // Check if we need to create a new chat or select an existing one
    console.log('Looking for chat interface');
    
    // Wait for the page to fully load
    await page.waitForTimeout(3000);
    
    // Take another screenshot to see the current state
    await page.screenshot({ path: 'chat-delete-emoji-test-page-loaded.png' });
    
    // Try to find the "Create New Chat" button if it exists
    const createNewChatButton = page.locator('button:has-text("Create New Chat"), button:has-text("New Chat")');
    if (await createNewChatButton.count() > 0) {
      console.log('Clicking Create New Chat button');
      await createNewChatButton.click();
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      await page.screenshot({ path: 'chat-delete-emoji-test-after-new-chat.png' });
    } else {
      console.log('No Create New Chat button found, looking for existing chats');
      
      // Try to find and click on an existing chat in the sidebar
      const existingChat = page.locator('.chat-item, .chat-list-item').first();
      if (await existingChat.count() > 0) {
        console.log('Clicking on existing chat');
        await existingChat.click();
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(2000);
        await page.screenshot({ path: 'chat-delete-emoji-test-after-select-chat.png' });
      } else {
        console.log('No existing chats found either');
        await page.screenshot({ path: 'chat-delete-emoji-test-no-chats-found.png' });
      }
    }
    
    // Look for any input field that might be used for chat
    console.log('Looking for chat input field');
    
    // Try different possible selectors for the chat input
    const inputSelectors = [
      'textarea.chat-input',
      'textarea[placeholder*="Type a message"]',
      'textarea[placeholder*="Send a message"]',
      'textarea[placeholder*="Ask"]',
      'textarea',
      'div[contenteditable="true"]',
      'input[type="text"]'
    ];
    
    let chatInput = null;
    for (const selector of inputSelectors) {
      const input = page.locator(selector);
      if (await input.count() > 0) {
        console.log(`Found chat input with selector: ${selector}`);
        chatInput = input;
        break;
      }
    }
    
    if (!chatInput) {
      console.log('Could not find chat input field');
      await page.screenshot({ path: 'chat-delete-emoji-test-no-input-found.png' });
      throw new Error('Chat input field not found');
    }
    
    // Type a message with emojis
    console.log('Typing a message with emojis');
    const messageWithEmojis = 'Hello! 😀 This is a test message with emojis 🚀 👍 🎉';
    await chatInput.fill(messageWithEmojis);
    
    // Take a screenshot after typing the message with emojis
    await page.screenshot({ path: 'chat-delete-emoji-test-with-emojis.png' });
    console.log('Saved screenshot after typing message with emojis');
    
    // Now delete the emojis one by one
    console.log('Deleting emojis from the message');
    
    // Method 1: Replace the message with a version without emojis
    const messageWithoutEmojis = 'Hello! This is a test message with emojis';
    await chatInput.fill(messageWithoutEmojis);
    
    // Take a screenshot after deleting emojis
    await page.screenshot({ path: 'chat-delete-emoji-test-without-emojis.png' });
    console.log('Saved screenshot after deleting emojis');
    
    // Method 2: Try to delete emojis using keyboard operations
    console.log('Trying to delete emojis using keyboard operations');
    
    // First, clear the input field
    await chatInput.fill('');
    
    // Type a new message with emojis
    const newMessageWithEmojis = 'Testing emoji deletion 😊 one by one 🎈';
    await chatInput.fill(newMessageWithEmojis);
    
    // Take a screenshot before keyboard deletion
    await page.screenshot({ path: 'chat-delete-emoji-test-before-keyboard-deletion.png' });
    
    // Position cursor before the first emoji and delete it
    // This is tricky with Playwright, but we can try
    
    // First, click at the beginning of the input
    await chatInput.click({ position: { x: 0, y: 0 } });
    
    // Press right arrow keys to navigate to before the first emoji
    for (let i = 0; i < 'Testing emoji deletion '.length; i++) {
      await page.keyboard.press('ArrowRight');
    }
    
    // Delete the emoji (which might be 2 characters)
    await page.keyboard.press('Delete');
    await page.keyboard.press('Delete');
    
    // Take a screenshot after deleting the first emoji
    await page.screenshot({ path: 'chat-delete-emoji-test-after-first-emoji-deletion.png' });
    
    // Navigate to before the second emoji
    for (let i = 0; i < ' one by one '.length; i++) {
      await page.keyboard.press('ArrowRight');
    }
    
    // Delete the second emoji
    await page.keyboard.press('Delete');
    await page.keyboard.press('Delete');
    
    // Take a screenshot after deleting the second emoji
    await page.screenshot({ path: 'chat-delete-emoji-test-after-second-emoji-deletion.png' });
    
    // Method 3: Replace with a completely new message
    console.log('Replacing with a new message without emojis');
    
    // Clear the input field
    await chatInput.fill('');
    
    // Type a message without emojis
    const finalMessage = 'This is a final message without any emojis, ready to be sent.';
    await chatInput.fill(finalMessage);
    
    // Take a screenshot before sending the message
    await page.screenshot({ path: 'chat-delete-emoji-test-before-send.png' });
    console.log('Saved screenshot before sending message');
    
    // Send the message - try pressing Enter first
    console.log('Sending the message');
    await chatInput.press('Enter');
    
    // Wait a moment to see if the message was sent
    await page.waitForTimeout(2000);
    await page.screenshot({ path: 'chat-delete-emoji-test-after-enter.png' });
    
    // If Enter didn't work, look for a send button
    const sendButton = page.locator('button[aria-label="Send message"], button:has-text("Send"), svg[aria-label="Send"]');
    if (await sendButton.count() > 0) {
      console.log('Clicking send button');
      await sendButton.click();
    }
    
    // Wait for a reasonable time for the message to be processed
    console.log('Waiting for message to be processed');
    await page.waitForTimeout(5000);
    
    // Take a screenshot after sending the message
    await page.screenshot({ path: 'chat-delete-emoji-test-after-send.png' });
    console.log('Saved screenshot after sending message');
    
    // Wait for the AI response (this might take some time)
    console.log('Waiting for AI response');
    await page.waitForTimeout(15000);
    
    // Take a screenshot of the AI response
    await page.screenshot({ path: 'chat-delete-emoji-test-ai-response.png' });
    console.log('Saved screenshot of AI response');
    
    console.log('Chat delete emoji test completed successfully');
  });
});
