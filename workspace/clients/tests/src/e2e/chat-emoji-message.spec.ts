import { test as base, expect } from '@playwright/test';
import path from 'path';
import fs from 'fs';

// Define a custom test that doesn't send Cloudflare headers to external services
const test = base.extend({
  context: async ({ browser }, use) => {
    // Create a context without the Cloudflare headers
    const context = await browser.newContext({
      extraHTTPHeaders: {} // Empty headers to override the default ones
    });
    await use(context);
  }
});

/**
 * E2E test for adding emojis in chat messages
 * This test verifies that a user can log in and send a message with emojis
 */
test.describe('Chat Emoji Messages', () => {
  test('should send a message with emojis successfully', async ({ page }) => {
    console.log('Starting chat emoji message test');
    
    // Navigate to the home page
    console.log('Navigating to home page');
    await page.goto('http://localhost:8080');
    await page.waitForLoadState('networkidle');
    
    // Take a screenshot of the home page
    await page.screenshot({ path: 'chat-emoji-test-home-page.png' });
    console.log('Saved screenshot of home page');
    
    // Check if we're already logged in
    const userMenu = page.locator('a.navbar-link[href="/user-group"]:has-text("Organization")');
    const isLoggedIn = await userMenu.count() > 0;
    
    if (!isLoggedIn) {
      console.log('Not logged in, logging in now');
      
      // Click the login button
      await page.click('button.button.is-text:has-text("Login")');
      
      // Wait for the login form to appear
      await page.waitForSelector('input[name="username"]');
      
      // Take a screenshot of the login form
      await page.screenshot({ path: 'chat-emoji-test-login-form.png' });
      console.log('Saved screenshot of login form');
      
      // Fill in login credentials
      console.log('Filling in login credentials');
      await page.fill('input[name="username"]', '<EMAIL>');
      await page.fill('input[name="password"]', '(abc123ABC)');
      
      // Click the login button
      console.log('Clicking submit button');
      await page.click('button[type="submit"]');
      
      // Short pause to allow for login processing
      console.log('Waiting briefly for login processing');
      await page.waitForTimeout(3000);
      
      // Wait for navigation to complete after login
      console.log('Waiting for navigation after login');
      await page.waitForLoadState('networkidle');
    } else {
      console.log('Already logged in, proceeding to chat');
    }
    
    // Navigate to the AI Chat page
    console.log('Navigating to AI Chat page');
    await page.goto('http://localhost:8080/ai-chat');
    await page.waitForLoadState('networkidle');
    
    // Take a screenshot of the AI Chat page
    await page.screenshot({ path: 'chat-emoji-test-ai-chat-page.png' });
    console.log('Saved screenshot of AI Chat page');
    
    // Check if we need to create a new chat or select an existing one
    console.log('Looking for chat interface');
    
    // Wait for the page to fully load
    await page.waitForTimeout(3000);
    
    // Take another screenshot to see the current state
    await page.screenshot({ path: 'chat-emoji-test-page-loaded.png' });
    
    // Try to find the "Create New Chat" button if it exists
    const createNewChatButton = page.locator('button:has-text("Create New Chat"), button:has-text("New Chat")');
    if (await createNewChatButton.count() > 0) {
      console.log('Clicking Create New Chat button');
      await createNewChatButton.click();
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      await page.screenshot({ path: 'chat-emoji-test-after-new-chat.png' });
    } else {
      console.log('No Create New Chat button found, looking for existing chats');
      
      // Try to find and click on an existing chat in the sidebar
      const existingChat = page.locator('.chat-item, .chat-list-item').first();
      if (await existingChat.count() > 0) {
        console.log('Clicking on existing chat');
        await existingChat.click();
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(2000);
        await page.screenshot({ path: 'chat-emoji-test-after-select-chat.png' });
      } else {
        console.log('No existing chats found either');
        await page.screenshot({ path: 'chat-emoji-test-no-chats-found.png' });
      }
    }
    
    // Look for any input field that might be used for chat
    console.log('Looking for chat input field');
    
    // Try different possible selectors for the chat input
    const inputSelectors = [
      'textarea.chat-input',
      'textarea[placeholder*="Type a message"]',
      'textarea[placeholder*="Send a message"]',
      'textarea[placeholder*="Ask"]',
      'textarea',
      'div[contenteditable="true"]',
      'input[type="text"]'
    ];
    
    let chatInput = null;
    for (const selector of inputSelectors) {
      const input = page.locator(selector);
      if (await input.count() > 0) {
        console.log(`Found chat input with selector: ${selector}`);
        chatInput = input;
        break;
      }
    }
    
    if (!chatInput) {
      console.log('Could not find chat input field');
      await page.screenshot({ path: 'chat-emoji-test-no-input-found.png' });
      throw new Error('Chat input field not found');
    }
    
    // Look for emoji button or picker
    console.log('Looking for emoji button or picker');
    
    // Try different possible selectors for emoji buttons
    const emojiButtonSelectors = [
      'button[aria-label="Emoji"], button[aria-label="Insert emoji"]',
      'button:has-text("😀"), button:has-text("🙂"), button:has-text("😊")',
      'button.emoji-button, button.emoji-picker-button',
      'svg[aria-label="Emoji"]',
      '.emoji-trigger'
    ];
    
    let emojiButton = null;
    for (const selector of emojiButtonSelectors) {
      const button = page.locator(selector);
      if (await button.count() > 0) {
        console.log(`Found emoji button with selector: ${selector}`);
        emojiButton = button;
        break;
      }
    }
    
    // Take a screenshot to see all available buttons
    await page.screenshot({ path: 'chat-emoji-test-looking-for-emoji-button.png' });
    
    // Method 1: If we found an emoji button, use it
    if (emojiButton) {
      console.log('Method 1: Using emoji button');
      
      // Click the emoji button
      await emojiButton.click();
      await page.waitForTimeout(1000);
      
      // Take a screenshot after clicking the emoji button
      await page.screenshot({ path: 'chat-emoji-test-emoji-picker-open.png' });
      
      // Try to find and click on an emoji in the picker
      const emojiSelectors = [
        '.emoji-picker .emoji',
        '.emoji-mart-emoji',
        '.emoji-item',
        'button.emoji'
      ];
      
      let foundEmoji = false;
      for (const selector of emojiSelectors) {
        const emojis = page.locator(selector);
        if (await emojis.count() > 0) {
          console.log(`Found emojis with selector: ${selector}`);
          await emojis.first().click();
          foundEmoji = true;
          break;
        }
      }
      
      if (!foundEmoji) {
        console.log('Could not find any emoji in the picker');
      }
      
      // Take a screenshot after selecting an emoji
      await page.screenshot({ path: 'chat-emoji-test-after-emoji-select.png' });
    } 
    
    // Method 2: If we didn't find an emoji button or couldn't select an emoji, type emojis directly
    console.log('Method 2: Typing emojis directly');
    
    // Clear the input field
    await chatInput.fill('');
    
    // Type a message with emojis
    const messageWithEmojis = 'Hello! 😀 This is a test message with emojis 🚀 👍 🎉';
    await chatInput.fill(messageWithEmojis);
    
    // Take a screenshot before sending the message
    await page.screenshot({ path: 'chat-emoji-test-before-send.png' });
    console.log('Saved screenshot before sending message');
    
    // Send the message - try pressing Enter first
    console.log('Sending the message');
    await chatInput.press('Enter');
    
    // Wait a moment to see if the message was sent
    await page.waitForTimeout(2000);
    await page.screenshot({ path: 'chat-emoji-test-after-enter.png' });
    
    // If Enter didn't work, look for a send button
    const sendButton = page.locator('button[aria-label="Send message"], button:has-text("Send"), svg[aria-label="Send"]');
    if (await sendButton.count() > 0) {
      console.log('Clicking send button');
      await sendButton.click();
    }
    
    // Wait for a reasonable time for the message to be processed
    console.log('Waiting for message to be processed');
    await page.waitForTimeout(5000);
    
    // Take a screenshot after sending the message
    await page.screenshot({ path: 'chat-emoji-test-after-send.png' });
    console.log('Saved screenshot after sending message');
    
    // Wait for the AI response (this might take some time)
    console.log('Waiting for AI response');
    await page.waitForTimeout(15000);
    
    // Take a screenshot of the AI response
    await page.screenshot({ path: 'chat-emoji-test-ai-response.png' });
    console.log('Saved screenshot of AI response');
    
    console.log('Chat emoji message test completed successfully');
  });
});
