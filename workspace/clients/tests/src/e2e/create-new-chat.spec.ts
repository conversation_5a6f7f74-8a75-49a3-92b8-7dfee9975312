import { test as base, expect } from '@playwright/test';
import path from 'path';
import fs from 'fs';

// Define a custom test that doesn't send Cloudflare headers to external services
const test = base.extend({
  context: async ({ browser }, use) => {
    // Create a context without the Cloudflare headers
    const context = await browser.newContext({
      extraHTTPHeaders: {} // Empty headers to override the default ones
    });
    await use(context);
  }
});

/**
 * E2E test for creating a new chat
 * This test verifies that a user can create a new chat and send a message
 */
test.describe('Create New Chat', () => {
  test('should create a new chat successfully', async ({ page }) => {
    console.log('Starting create new chat test');
    
    // Navigate to the home page
    console.log('Navigating to home page');
    await page.goto('http://localhost:8080');
    await page.waitForLoadState('networkidle');
    
    // Take a screenshot of the home page
    await page.screenshot({ path: 'create-new-chat-test-home-page.png' });
    console.log('Saved screenshot of home page');
    
    // Check if we're already logged in
    const userMenu = page.locator('a.navbar-link[href="/user-group"]:has-text("Organization")');
    const isLoggedIn = await userMenu.count() > 0;
    
    if (!isLoggedIn) {
      console.log('Not logged in, logging in now');
      
      // Click the login button
      await page.click('button.button.is-text:has-text("Login")');
      
      // Wait for the login form to appear
      await page.waitForSelector('input[name="username"]');
      
      // Take a screenshot of the login form
      await page.screenshot({ path: 'create-new-chat-test-login-form.png' });
      console.log('Saved screenshot of login form');
      
      // Fill in login credentials
      console.log('Filling in login credentials');
      await page.fill('input[name="username"]', '<EMAIL>');
      await page.fill('input[name="password"]', '(abc123ABC)');
      
      // Click the login button
      console.log('Clicking submit button');
      await page.click('button[type="submit"]');
      
      // Short pause to allow for login processing
      console.log('Waiting briefly for login processing');
      await page.waitForTimeout(3000);
      
      // Wait for navigation to complete after login
      console.log('Waiting for navigation after login');
      await page.waitForLoadState('networkidle');
    } else {
      console.log('Already logged in, proceeding to chat');
    }
    
    // Navigate to the AI Chat page
    console.log('Navigating to AI Chat page');
    await page.goto('http://localhost:8080/ai-chat');
    await page.waitForLoadState('networkidle');
    
    // Take a screenshot of the AI Chat page
    await page.screenshot({ path: 'create-new-chat-test-ai-chat-page.png' });
    console.log('Saved screenshot of AI Chat page');
    
    // Wait for the page to fully load
    await page.waitForTimeout(3000);
    
    // Look for the "Create New Chat" button or similar
    console.log('Looking for Create New Chat button');
    
    // Try different possible selectors for the create new chat button
    const newChatButtonSelectors = [
      'button:has-text("Create New Chat")',
      'button:has-text("New Chat")',
      'button.new-chat-button',
      'a:has-text("New Chat")',
      'a.new-chat',
      'button[aria-label="New Chat"]',
      'svg[aria-label="New Chat"]',
      '.new-chat-trigger'
    ];
    
    let newChatButton = null;
    for (const selector of newChatButtonSelectors) {
      const button = page.locator(selector);
      if (await button.count() > 0) {
        console.log(`Found new chat button with selector: ${selector}`);
        newChatButton = button;
        break;
      }
    }
    
    // Take a screenshot to see all available buttons
    await page.screenshot({ path: 'create-new-chat-test-looking-for-button.png' });
    
    if (!newChatButton) {
      // If we can't find a dedicated new chat button, look for any button that might be related
      console.log('No dedicated new chat button found, looking for any button that might be related');
      
      // Look for buttons with "+" or "add" or similar
      const alternativeButtonSelectors = [
        'button:has-text("+")',
        'button.add-button',
        'button[aria-label="Add"]',
        'svg[aria-label="Add"]',
        '.add-trigger',
        'button.create-button',
        'button[aria-label="Create"]'
      ];
      
      for (const selector of alternativeButtonSelectors) {
        const button = page.locator(selector);
        if (await button.count() > 0) {
          console.log(`Found alternative button with selector: ${selector}`);
          newChatButton = button;
          break;
        }
      }
      
      // If we still can't find a button, look for any prominent button
      if (!newChatButton) {
        console.log('No alternative button found, looking for any prominent button');
        
        const buttons = page.locator('button');
        const count = await buttons.count();
        
        console.log(`Found ${count} buttons on the page`);
        
        // Take screenshots of the first few buttons
        for (let i = 0; i < Math.min(count, 5); i++) {
          const button = buttons.nth(i);
          const buttonText = await button.textContent() || '';
          const buttonClass = await button.getAttribute('class') || '';
          const buttonAriaLabel = await button.getAttribute('aria-label') || '';
          
          console.log(`Button ${i}: Text="${buttonText}", Class="${buttonClass}", AriaLabel="${buttonAriaLabel}"`);
          
          // If this button looks like it might be related to creating a new chat, use it
          if (
            buttonText.toLowerCase().includes('new') ||
            buttonText.toLowerCase().includes('create') ||
            buttonText.toLowerCase().includes('add') ||
            buttonText.toLowerCase().includes('+') ||
            buttonClass.toLowerCase().includes('new') ||
            buttonClass.toLowerCase().includes('create') ||
            buttonClass.toLowerCase().includes('add') ||
            buttonAriaLabel.toLowerCase().includes('new') ||
            buttonAriaLabel.toLowerCase().includes('create') ||
            buttonAriaLabel.toLowerCase().includes('add')
          ) {
            console.log(`Button ${i} looks like it might be related to creating a new chat`);
            newChatButton = button;
            break;
          }
        }
      }
    }
    
    if (newChatButton) {
      // Click the new chat button
      console.log('Clicking new chat button');
      await newChatButton.click();
      
      // Wait for the new chat to be created
      await page.waitForTimeout(2000);
      
      // Take a screenshot after clicking the new chat button
      await page.screenshot({ path: 'create-new-chat-test-after-button-click.png' });
      console.log('Saved screenshot after clicking new chat button');
    } else {
      console.log('Could not find any button to create a new chat');
      await page.screenshot({ path: 'create-new-chat-test-no-button-found.png' });
      
      // Try an alternative approach - check if we can directly start typing in a chat input
      console.log('Trying alternative approach - looking for chat input field');
      
      // Try different possible selectors for the chat input
      const inputSelectors = [
        'textarea.chat-input',
        'textarea[placeholder*="Type a message"]',
        'textarea[placeholder*="Send a message"]',
        'textarea[placeholder*="Ask"]',
        'textarea',
        'div[contenteditable="true"]',
        'input[type="text"]'
      ];
      
      let chatInput = null;
      for (const selector of inputSelectors) {
        const input = page.locator(selector);
        if (await input.count() > 0) {
          console.log(`Found chat input with selector: ${selector}`);
          chatInput = input;
          break;
        }
      }
      
      if (!chatInput) {
        console.log('Could not find chat input field');
        await page.screenshot({ path: 'create-new-chat-test-no-input-found.png' });
        throw new Error('Could not find a way to create a new chat');
      }
      
      console.log('Found chat input field, proceeding with test');
    }
    
    // Look for the chat input field
    console.log('Looking for chat input field');
    
    // Try different possible selectors for the chat input
    const inputSelectors = [
      'textarea.chat-input',
      'textarea[placeholder*="Type a message"]',
      'textarea[placeholder*="Send a message"]',
      'textarea[placeholder*="Ask"]',
      'textarea',
      'div[contenteditable="true"]',
      'input[type="text"]'
    ];
    
    let chatInput = null;
    for (const selector of inputSelectors) {
      const input = page.locator(selector);
      if (await input.count() > 0) {
        console.log(`Found chat input with selector: ${selector}`);
        chatInput = input;
        break;
      }
    }
    
    if (!chatInput) {
      console.log('Could not find chat input field');
      await page.screenshot({ path: 'create-new-chat-test-no-input-found.png' });
      throw new Error('Chat input field not found');
    }
    
    // Type a message in the new chat
    console.log('Typing a message in the new chat');
    const message = 'Hello! This is a message in a new chat.';
    await chatInput.fill(message);
    
    // Take a screenshot before sending the message
    await page.screenshot({ path: 'create-new-chat-test-before-send.png' });
    console.log('Saved screenshot before sending message');
    
    // Send the message - try pressing Enter first
    console.log('Sending the message');
    await chatInput.press('Enter');
    
    // Wait a moment to see if the message was sent
    await page.waitForTimeout(2000);
    await page.screenshot({ path: 'create-new-chat-test-after-enter.png' });
    
    // If Enter didn't work, look for a send button
    const sendButton = page.locator('button[aria-label="Send message"], button:has-text("Send"), svg[aria-label="Send"]');
    if (await sendButton.count() > 0) {
      console.log('Clicking send button');
      await sendButton.click();
    }
    
    // Wait for a reasonable time for the message to be processed
    console.log('Waiting for message to be processed');
    await page.waitForTimeout(5000);
    
    // Take a screenshot after sending the message
    await page.screenshot({ path: 'create-new-chat-test-after-send.png' });
    console.log('Saved screenshot after sending message');
    
    // Wait for the AI response (this might take some time)
    console.log('Waiting for AI response');
    await page.waitForTimeout(15000);
    
    // Take a screenshot of the AI response
    await page.screenshot({ path: 'create-new-chat-test-ai-response.png' });
    console.log('Saved screenshot of AI response');
    
    console.log('Create new chat test completed successfully');
  });
});
