import { test as base, expect } from '@playwright/test';
import path from 'path';
import fs from 'fs';

// Define a custom test that doesn't send Cloudflare headers to external services
const test = base.extend({
  context: async ({ browser }, use) => {
    // Create a context without the Cloudflare headers
    const context = await browser.newContext({
      extraHTTPHeaders: {} // Empty headers to override the default ones
    });
    await use(context);
  }
});

/**
 * E2E test for deleting a chat
 * This test verifies that a user can delete an existing chat
 */
test.describe('Delete Chat', () => {
  test('should delete a chat successfully', async ({ page }) => {
    console.log('Starting delete chat test');

    // Navigate to the home page
    console.log('Navigating to home page');
    await page.goto('http://localhost:8080');
    await page.waitForLoadState('networkidle');

    // Take a screenshot of the home page
    await page.screenshot({ path: 'delete-chat-test-home-page.png' });
    console.log('Saved screenshot of home page');

    // Check if we're already logged in
    const userMenu = page.locator('a.navbar-link[href="/user-group"]:has-text("Organization")');
    const isLoggedIn = await userMenu.count() > 0;

    if (!isLoggedIn) {
      console.log('Not logged in, logging in now');

      // Click the login button
      await page.click('button.button.is-text:has-text("Login")');

      // Wait for the login form to appear
      await page.waitForSelector('input[name="username"]');

      // Take a screenshot of the login form
      await page.screenshot({ path: 'delete-chat-test-login-form.png' });
      console.log('Saved screenshot of login form');

      // Fill in login credentials
      console.log('Filling in login credentials');
      await page.fill('input[name="username"]', '<EMAIL>');
      await page.fill('input[name="password"]', '(abc123ABC)');

      // Click the login button
      console.log('Clicking submit button');
      await page.click('button[type="submit"]');

      // Short pause to allow for login processing
      console.log('Waiting briefly for login processing');
      await page.waitForTimeout(3000);

      // Wait for navigation to complete after login
      console.log('Waiting for navigation after login');
      await page.waitForLoadState('networkidle');
    } else {
      console.log('Already logged in, proceeding to chat');
    }

    // Navigate to the AI Chat page
    console.log('Navigating to AI Chat page');
    await page.goto('http://localhost:8080/ai-chat');
    await page.waitForLoadState('networkidle');

    // Take a screenshot of the AI Chat page
    await page.screenshot({ path: 'delete-chat-test-ai-chat-page.png' });
    console.log('Saved screenshot of AI Chat page');

    // Wait for the page to fully load
    await page.waitForTimeout(3000);

    // First, we need to create a new chat or ensure we have a chat to delete
    console.log('Ensuring we have a chat to delete');

    // Look for the chat input field
    const chatInput = page.locator('textarea, div[contenteditable="true"], input[type="text"]').first();

    if (await chatInput.count() > 0) {
      console.log('Found chat input field, creating a new message');

      // Type a message in the chat
      const message = 'This is a test chat that will be deleted.';
      await chatInput.fill(message);

      // Send the message
      await chatInput.press('Enter');

      // If Enter didn't work, look for a send button
      const sendButton = page.locator('button[aria-label="Send message"], button:has-text("Send"), svg[aria-label="Send"]');
      if (await sendButton.count() > 0) {
        console.log('Clicking send button');
        await sendButton.click();
      }

      // Wait for the message to be sent and the response to come back
      await page.waitForTimeout(10000);

      // Take a screenshot after creating the chat
      await page.screenshot({ path: 'delete-chat-test-chat-created.png' });
      console.log('Saved screenshot after creating chat');
    } else {
      console.log('Could not find chat input field');
      await page.screenshot({ path: 'delete-chat-test-no-input-found.png' });
      throw new Error('Chat input field not found');
    }

    // Now look for delete buttons or options
    console.log('Looking for delete chat button or option');

    // Try different possible selectors for delete buttons
    const deleteButtonSelectors = [
      'button:has-text("Delete Chat")',
      'button:has-text("Delete")',
      'button[aria-label="Delete Chat"]',
      'button[aria-label="Delete"]',
      'svg[aria-label="Delete"]',
      '.delete-chat-button',
      'button.delete-button',
      'button:has-text("🗑️")',
      'button.trash-button'
    ];

    let deleteButton = null;
    for (const selector of deleteButtonSelectors) {
      const button = page.locator(selector);
      if (await button.count() > 0) {
        console.log(`Found delete button with selector: ${selector}`);
        deleteButton = button;
        break;
      }
    }

    // Take a screenshot to see all available buttons
    await page.screenshot({ path: 'delete-chat-test-looking-for-delete-button.png' });

    if (!deleteButton) {
      // If we can't find a dedicated delete button, look for menu buttons that might contain delete options
      console.log('No dedicated delete button found, looking for menu buttons');

      const menuButtonSelectors = [
        'button:has-text("⋮")',
        'button:has-text("...")',
        'button[aria-label="Menu"]',
        'button[aria-label="Options"]',
        'svg[aria-label="Menu"]',
        'svg[aria-label="Options"]',
        '.menu-button',
        '.options-button',
        'button.menu',
        'button.options',
        'button:has-text("⚙")',
        'button.settings-button'
      ];

      let menuButton = null;
      for (const selector of menuButtonSelectors) {
        const button = page.locator(selector);
        if (await button.count() > 0) {
          console.log(`Found menu button with selector: ${selector}`);
          menuButton = button;
          break;
        }
      }

      if (menuButton) {
        // Click the menu button
        console.log('Clicking menu button');
        await menuButton.click();

        // Wait for the menu to appear
        await page.waitForTimeout(1000);

        // Take a screenshot after clicking the menu button
        await page.screenshot({ path: 'delete-chat-test-menu-opened.png' });

        // Look for delete option in the menu
        const deleteOptionSelectors = [
          'button:has-text("Delete")',
          'a:has-text("Delete")',
          'div:has-text("Delete")',
          'li:has-text("Delete")',
          '.delete-option',
          '[role="menuitem"]:has-text("Delete")'
        ];

        let deleteOption = null;
        for (const selector of deleteOptionSelectors) {
          const option = page.locator(selector);
          if (await option.count() > 0) {
            console.log(`Found delete option with selector: ${selector}`);
            deleteOption = option;
            break;
          }
        }

        if (deleteOption) {
          deleteButton = deleteOption;
        }
      }
    }

    // If we still can't find a delete button, look for any button that might be related to deletion
    if (!deleteButton) {
      console.log('No menu button or delete option found, looking for any button that might be related to deletion');

      const buttons = page.locator('button');
      const count = await buttons.count();

      console.log(`Found ${count} buttons on the page`);

      // Take screenshots of the first few buttons
      for (let i = 0; i < Math.min(count, 10); i++) {
        const button = buttons.nth(i);
        const buttonText = await button.textContent() || '';
        const buttonClass = await button.getAttribute('class') || '';
        const buttonAriaLabel = await button.getAttribute('aria-label') || '';

        console.log(`Button ${i}: Text="${buttonText}", Class="${buttonClass}", AriaLabel="${buttonAriaLabel}"`);

        // If this button looks like it might be related to deletion, use it
        if (
          buttonText.includes('🗑️') ||
          buttonText.toLowerCase().includes('delete') ||
          buttonText.toLowerCase().includes('remove') ||
          buttonClass.toLowerCase().includes('delete') ||
          buttonClass.toLowerCase().includes('remove') ||
          buttonClass.toLowerCase().includes('trash') ||
          buttonAriaLabel.toLowerCase().includes('delete') ||
          buttonAriaLabel.toLowerCase().includes('remove') ||
          buttonAriaLabel.toLowerCase().includes('trash')
        ) {
          console.log(`Button ${i} looks like it might be related to deletion`);
          deleteButton = button;
          break;
        }
      }
    }

    if (deleteButton) {
      // We found multiple delete buttons, so we need to select a specific chat to delete
      console.log('Found multiple delete buttons, selecting a specific chat to delete');

      // Look for chat items in the sidebar
      const chatItems = page.locator('.chat-item, .chat-list-item, li');
      const chatCount = await chatItems.count();

      console.log(`Found ${chatCount} chat items`);

      if (chatCount > 0) {
        // Find the chat item that contains our test message or select the first one
        let chatToDelete = null;
        let chatDeleteButton = null;

        for (let i = 0; i < chatCount; i++) {
          const chatItem = chatItems.nth(i);
          const chatText = await chatItem.textContent() || '';

          console.log(`Chat ${i}: Text="${chatText}"`);

          // Look for a chat that has a delete button (🗑️)
          if (chatText.includes('🗑️')) {
            console.log(`Found chat ${i} with delete button: "${chatText}"`);
            chatToDelete = chatItem;

            // Find the delete button within this chat item
            const deleteButtonInChat = chatItem.locator('button:has-text("🗑️"), button[aria-label="Delete Title"]');
            if (await deleteButtonInChat.count() > 0) {
              console.log(`Found delete button in chat ${i}`);
              chatDeleteButton = deleteButtonInChat;
              break;
            }
          }
        }

        // If we couldn't find a chat with a delete button, try a different approach
        if (!chatToDelete || !chatDeleteButton) {
          console.log('Could not find a chat with a delete button, trying a different approach');

          // Try to find any chat with a delete button
          const chatItemsWithDeleteButton = page.locator('li:has(button:has-text("🗑️")), li:has(button[aria-label="Delete Title"])');
          const chatItemsWithDeleteButtonCount = await chatItemsWithDeleteButton.count();

          console.log(`Found ${chatItemsWithDeleteButtonCount} chat items with delete buttons`);

          if (chatItemsWithDeleteButtonCount > 0) {
            // Select the first chat with a delete button
            chatToDelete = chatItemsWithDeleteButton.first();
            const deleteButtonInChat = chatToDelete.locator('button:has-text("🗑️"), button[aria-label="Delete Title"]');
            if (await deleteButtonInChat.count() > 0) {
              console.log('Found delete button in the first chat with a delete button');
              chatDeleteButton = deleteButtonInChat;
            }
          }
        }

        if (chatToDelete) {
          // First, click on the chat to select it
          console.log('Clicking on the chat to select it');
          await chatToDelete.click();

          // Wait for the chat to be selected
          await page.waitForTimeout(1000);

          // Take a screenshot after selecting the chat
          await page.screenshot({ path: 'delete-chat-test-after-chat-select.png' });

          // Now look for the delete button again, which should be visible after selecting the chat
          console.log('Looking for delete button after selecting the chat');

          // Try different approaches to find the delete button
          const deleteButtonSelectors = [
            'button:has-text("🗑️")',
            'button[aria-label="Delete Title"]',
            'button[aria-label="Delete"]',
            'button.delete-button',
            'button.trash-button',
            'svg[aria-label="Delete"]'
          ];

          let visibleDeleteButton = null;
          for (const selector of deleteButtonSelectors) {
            const buttons = page.locator(selector);
            const count = await buttons.count();

            for (let i = 0; i < count; i++) {
              const button = buttons.nth(i);
              if (await button.isVisible()) {
                console.log(`Found visible delete button with selector: ${selector}`);
                visibleDeleteButton = button;
                break;
              }
            }

            if (visibleDeleteButton) break;
          }

          if (visibleDeleteButton) {
            // Click the delete button
            console.log('Clicking delete button');
            await visibleDeleteButton.click();

            // Wait for any confirmation dialog
            await page.waitForTimeout(1000);

            // Take a screenshot after clicking the delete button
            await page.screenshot({ path: 'delete-chat-test-after-delete-click.png' });

            // Look for a confirmation dialog
            const confirmButtonSelectors = [
              'button:has-text("Confirm")',
              'button:has-text("Yes")',
              'button:has-text("OK")',
              'button:has-text("Delete")',
              'button.confirm-button',
              'button.yes-button',
              'button.ok-button',
              'button.delete-confirm-button'
            ];

            let confirmButton = null;
            for (const selector of confirmButtonSelectors) {
              const button = page.locator(selector);
              if (await button.count() > 0 && await button.isVisible()) {
                console.log(`Found confirm button with selector: ${selector}`);
                confirmButton = button;
                break;
              }
            }

            if (confirmButton) {
              // Click the confirm button
              console.log('Clicking confirm button');
              await confirmButton.click();

              // Wait for the deletion to complete
              await page.waitForTimeout(2000);
            }

            // Take a screenshot after deletion
            await page.screenshot({ path: 'delete-chat-test-after-deletion.png' });
            console.log('Saved screenshot after deletion');

            console.log('Delete chat test completed successfully');
          } else {
            console.log('Could not find a visible delete button after selecting the chat');
            await page.screenshot({ path: 'delete-chat-test-no-visible-delete-button.png' });

            // Try a different approach - look for any visible button that might be related to deletion
            console.log('Looking for any visible button that might be related to deletion');

            const allButtons = page.locator('button');
            const buttonCount = await allButtons.count();

            for (let i = 0; i < buttonCount; i++) {
              const button = allButtons.nth(i);
              if (await button.isVisible()) {
                const buttonText = await button.textContent() || '';
                const buttonClass = await button.getAttribute('class') || '';
                const buttonAriaLabel = await button.getAttribute('aria-label') || '';

                console.log(`Visible button ${i}: Text="${buttonText}", Class="${buttonClass}", AriaLabel="${buttonAriaLabel}"`);

                // If this button looks like it might be related to deletion, use it
                if (
                  buttonText.includes('🗑️') ||
                  buttonText.toLowerCase().includes('delete') ||
                  buttonText.toLowerCase().includes('remove') ||
                  buttonClass.toLowerCase().includes('delete') ||
                  buttonClass.toLowerCase().includes('remove') ||
                  buttonClass.toLowerCase().includes('trash') ||
                  buttonAriaLabel.toLowerCase().includes('delete') ||
                  buttonAriaLabel.toLowerCase().includes('remove') ||
                  buttonAriaLabel.toLowerCase().includes('trash')
                ) {
                  console.log(`Button ${i} looks like it might be related to deletion, clicking it`);
                  await button.click();

                  // Wait for any confirmation dialog
                  await page.waitForTimeout(1000);

                  // Take a screenshot after clicking the button
                  await page.screenshot({ path: 'delete-chat-test-after-alternative-button-click.png' });
                  break;
                }
              }
            }
          }
        } else {
          console.log('Could not find a chat to delete');
          await page.screenshot({ path: 'delete-chat-test-no-chat-to-delete.png' });
          throw new Error('Chat to delete not found');
        }
      } else {
        console.log('Could not find any chat items');
        await page.screenshot({ path: 'delete-chat-test-no-chat-items.png' });
        throw new Error('No chat items found');
      }
    } else {
      console.log('Could not find any button to delete the chat');
      await page.screenshot({ path: 'delete-chat-test-no-delete-button-found.png' });
      throw new Error('Delete button not found');
    }
  });
});
