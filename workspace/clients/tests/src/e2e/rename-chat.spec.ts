import { test as base, expect } from '@playwright/test';
import path from 'path';
import fs from 'fs';

// Define a custom test that doesn't send Cloudflare headers to external services
const test = base.extend({
  context: async ({ browser }, use) => {
    // Create a context without the Cloudflare headers
    const context = await browser.newContext({
      extraHTTPHeaders: {} // Empty headers to override the default ones
    });
    await use(context);
  }
});

/**
 * E2E test for renaming a chat
 * This test verifies that a user can rename an existing chat
 */
test.describe('Rename Chat', () => {
  test('should rename a chat successfully', async ({ page }) => {
    console.log('Starting rename chat test');
    
    // Navigate to the home page
    console.log('Navigating to home page');
    await page.goto('http://localhost:8080');
    await page.waitForLoadState('networkidle');
    
    // Take a screenshot of the home page
    await page.screenshot({ path: 'rename-chat-test-home-page.png' });
    console.log('Saved screenshot of home page');
    
    // Check if we're already logged in
    const userMenu = page.locator('a.navbar-link[href="/user-group"]:has-text("Organization")');
    const isLoggedIn = await userMenu.count() > 0;
    
    if (!isLoggedIn) {
      console.log('Not logged in, logging in now');
      
      // Click the login button
      await page.click('button.button.is-text:has-text("Login")');
      
      // Wait for the login form to appear
      await page.waitForSelector('input[name="username"]');
      
      // Take a screenshot of the login form
      await page.screenshot({ path: 'rename-chat-test-login-form.png' });
      console.log('Saved screenshot of login form');
      
      // Fill in login credentials
      console.log('Filling in login credentials');
      await page.fill('input[name="username"]', '<EMAIL>');
      await page.fill('input[name="password"]', '(abc123ABC)');
      
      // Click the login button
      console.log('Clicking submit button');
      await page.click('button[type="submit"]');
      
      // Short pause to allow for login processing
      console.log('Waiting briefly for login processing');
      await page.waitForTimeout(3000);
      
      // Wait for navigation to complete after login
      console.log('Waiting for navigation after login');
      await page.waitForLoadState('networkidle');
    } else {
      console.log('Already logged in, proceeding to chat');
    }
    
    // Navigate to the AI Chat page
    console.log('Navigating to AI Chat page');
    await page.goto('http://localhost:8080/ai-chat');
    await page.waitForLoadState('networkidle');
    
    // Take a screenshot of the AI Chat page
    await page.screenshot({ path: 'rename-chat-test-ai-chat-page.png' });
    console.log('Saved screenshot of AI Chat page');
    
    // Wait for the page to fully load
    await page.waitForTimeout(3000);
    
    // First, we need to create a new chat or ensure we have a chat to rename
    console.log('Ensuring we have a chat to rename');
    
    // Look for the chat input field
    const chatInput = page.locator('textarea, div[contenteditable="true"], input[type="text"]').first();
    
    if (await chatInput.count() > 0) {
      console.log('Found chat input field, creating a new message');
      
      // Type a message in the chat
      const message = 'This is a test chat that will be renamed.';
      await chatInput.fill(message);
      
      // Send the message
      await chatInput.press('Enter');
      
      // If Enter didn't work, look for a send button
      const sendButton = page.locator('button[aria-label="Send message"], button:has-text("Send"), svg[aria-label="Send"]');
      if (await sendButton.count() > 0) {
        console.log('Clicking send button');
        await sendButton.click();
      }
      
      // Wait for the message to be sent and the response to come back
      await page.waitForTimeout(10000);
      
      // Take a screenshot after creating the chat
      await page.screenshot({ path: 'rename-chat-test-chat-created.png' });
      console.log('Saved screenshot after creating chat');
    } else {
      console.log('Could not find chat input field');
      await page.screenshot({ path: 'rename-chat-test-no-input-found.png' });
      throw new Error('Chat input field not found');
    }
    
    // Now look for a chat to rename
    console.log('Looking for a chat to rename');
    
    // Look for chat items in the sidebar
    const chatItems = page.locator('.chat-item, .chat-list-item, li');
    const chatCount = await chatItems.count();
    
    console.log(`Found ${chatCount} chat items`);
    
    if (chatCount > 0) {
      // Find a chat item to rename (preferably the first one)
      let chatToRename = chatItems.first();
      
      // Click on the chat to select it
      console.log('Clicking on the chat to select it');
      await chatToRename.click();
      
      // Wait for the chat to be selected
      await page.waitForTimeout(1000);
      
      // Take a screenshot after selecting the chat
      await page.screenshot({ path: 'rename-chat-test-after-chat-select.png' });
      
      // Now look for rename button or edit title button
      console.log('Looking for rename button or edit title button');
      
      // Try different possible selectors for rename buttons
      const renameButtonSelectors = [
        'button:has-text("Rename")',
        'button:has-text("Edit")',
        'button[aria-label="Rename Chat"]',
        'button[aria-label="Edit Title"]',
        'button[aria-label="Edit"]',
        'svg[aria-label="Edit"]',
        '.rename-button',
        '.edit-button',
        'button:has-text("✏️")',
        'button.edit-title-button'
      ];
      
      let renameButton = null;
      for (const selector of renameButtonSelectors) {
        const buttons = page.locator(selector);
        const count = await buttons.count();
        
        for (let i = 0; i < count; i++) {
          const button = buttons.nth(i);
          if (await button.isVisible()) {
            console.log(`Found visible rename button with selector: ${selector}`);
            renameButton = button;
            break;
          }
        }
        
        if (renameButton) break;
      }
      
      // If we can't find a dedicated rename button, look for the title itself which might be editable
      if (!renameButton) {
        console.log('No dedicated rename button found, looking for editable title');
        
        const titleSelectors = [
          '.chat-title',
          '.title',
          'h1',
          'h2',
          'h3',
          '.chat-name',
          '.chat-header'
        ];
        
        let editableTitle = null;
        for (const selector of titleSelectors) {
          const titles = page.locator(selector);
          const count = await titles.count();
          
          for (let i = 0; i < count; i++) {
            const title = titles.nth(i);
            if (await title.isVisible()) {
              console.log(`Found visible title with selector: ${selector}`);
              editableTitle = title;
              break;
            }
          }
          
          if (editableTitle) break;
        }
        
        if (editableTitle) {
          // Try to click on the title to see if it becomes editable
          console.log('Clicking on the title to see if it becomes editable');
          await editableTitle.click();
          
          // Wait a moment to see if the title becomes editable
          await page.waitForTimeout(1000);
          
          // Take a screenshot after clicking the title
          await page.screenshot({ path: 'rename-chat-test-after-title-click.png' });
        }
      } else {
        // Click the rename button
        console.log('Clicking rename button');
        await renameButton.click();
        
        // Wait for the rename input to appear
        await page.waitForTimeout(1000);
        
        // Take a screenshot after clicking the rename button
        await page.screenshot({ path: 'rename-chat-test-after-rename-click.png' });
      }
      
      // Look for an input field to rename the chat
      console.log('Looking for input field to rename the chat');
      
      // Try different possible selectors for the rename input
      const renameInputSelectors = [
        'input[type="text"]',
        'input.rename-input',
        'input.edit-title-input',
        'input.title-input',
        'div[contenteditable="true"]',
        'textarea'
      ];
      
      let renameInput = null;
      for (const selector of renameInputSelectors) {
        const inputs = page.locator(selector);
        const count = await inputs.count();
        
        for (let i = 0; i < count; i++) {
          const input = inputs.nth(i);
          if (await input.isVisible()) {
            console.log(`Found visible rename input with selector: ${selector}`);
            renameInput = input;
            break;
          }
        }
        
        if (renameInput) break;
      }
      
      if (renameInput) {
        // Clear the input field
        await renameInput.fill('');
        
        // Type a new name for the chat
        const newChatName = 'Renamed Chat ' + new Date().toISOString().slice(0, 19).replace('T', ' ');
        console.log(`Typing new chat name: ${newChatName}`);
        await renameInput.fill(newChatName);
        
        // Take a screenshot after typing the new name
        await page.screenshot({ path: 'rename-chat-test-after-typing-name.png' });
        
        // Press Enter to confirm the rename
        console.log('Pressing Enter to confirm the rename');
        await renameInput.press('Enter');
        
        // Wait for the rename to complete
        await page.waitForTimeout(2000);
        
        // If Enter didn't work, look for a save or confirm button
        const saveButtonSelectors = [
          'button:has-text("Save")',
          'button:has-text("Confirm")',
          'button:has-text("OK")',
          'button.save-button',
          'button.confirm-button',
          'button.ok-button'
        ];
        
        let saveButton = null;
        for (const selector of saveButtonSelectors) {
          const buttons = page.locator(selector);
          const count = await buttons.count();
          
          for (let i = 0; i < count; i++) {
            const button = buttons.nth(i);
            if (await button.isVisible()) {
              console.log(`Found visible save button with selector: ${selector}`);
              saveButton = button;
              break;
            }
          }
          
          if (saveButton) break;
        }
        
        if (saveButton) {
          // Click the save button
          console.log('Clicking save button');
          await saveButton.click();
          
          // Wait for the save to complete
          await page.waitForTimeout(2000);
        }
        
        // Take a screenshot after renaming
        await page.screenshot({ path: 'rename-chat-test-after-rename.png' });
        console.log('Saved screenshot after renaming');
        
        console.log('Rename chat test completed successfully');
      } else {
        console.log('Could not find input field to rename the chat');
        await page.screenshot({ path: 'rename-chat-test-no-rename-input.png' });
        throw new Error('Rename input field not found');
      }
    } else {
      console.log('Could not find any chat items');
      await page.screenshot({ path: 'rename-chat-test-no-chat-items.png' });
      throw new Error('No chat items found');
    }
  });
});
