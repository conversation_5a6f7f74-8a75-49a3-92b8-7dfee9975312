{"name": "@divinci-ai/web-client", "version": "0.3.0", "private": true, "description": "The web client for divinci", "main": "./dist/index.js", "scripts": {"build": "webpack --mode development --config webpack.config.ci.js", "build:ignore-errors": "webpack --mode development || echo 'ℹ️ clients/web Build errors ignored. '", "build:ignore-errors:ci": "webpack --mode development || echo 'ℹ️ clients/web Build errors ignored. '", "start": "node server.js", "start:dev": "webpack serve --open", "start:dev:http": "webpack serve --open", "start:dev:build": "webpack --watch --config webpack.config.js", "prepare": "rimraf ./dist && tsc --skipLibCheck", "prepare:ci": "rimraf ./dist && tsc --skipLibCheck --project tsconfig.ci.json", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:e2e:local": "TEST_ENV=local ../../../scripts/e2e-docker-compose.sh local", "test:e2e:production": "TEST_ENV=production ../../../scripts/e2e-docker-compose.sh production"}, "author": "Divinci AI", "license": "JSON", "dependencies": {"@ag-grid-community/client-side-row-model": "^32.2.1", "@ag-grid-community/core": "^32.2.1", "@ag-grid-community/react": "^32.2.1", "@ag-grid-community/styles": "^32.2.1", "@auth0/auth0-react": "^2.2.4", "@aws-sdk/client-s3": "^3.750.0", "@datadog/browser-rum": "^5.32.0", "@divinci-ai/actions": "file:../../resources/actions", "@divinci-ai/models": "file:../../resources/models", "@divinci-ai/tools": "file:../../resources/tools", "@divinci-ai/utils": "file:../../resources/utils", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@mui/material": "^5.15.15", "@speechly/speech-recognition-polyfill": "^1.3.0", "buffer": "^6.0.3", "bulma": "^1.0.2", "classnames": "^2.5.1", "cors": "^2.8.5", "csv-parser": "^3.0.0", "dompurify": "^3.1.6", "emoji-picker-react": "^4.12.2", "emoji-regex": "^10.3.0", "express": "^4.19.2", "gpt-tokens": "^1.3.12", "jszip": "^3.10.1", "markdown-to-text": "^0.1.1", "mermaid": "^10.9.1", "moment": "^2.30.1", "morgan": "^1.10.0", "node-html-markdown": "^1.3.0", "react": "^18.3.1", "react-dom": "^18.3.0", "react-force-graph-2d": "^1.25.4", "react-markdown": "^9.0.1", "react-multi-carousel": "^2.8.5", "react-phone-number-input": "^3.3.9", "react-router": "^6.22.3", "react-router-dom": "^6.22.3", "react-speech-recognition": "^3.10.0", "react-tooltip": "^5.28.0", "regenerator-runtime": "^0.14.1", "stream-browserify": "^3.0.0", "svg2png-wasm": "^1.4.1", "uuid": "^9.0.1", "yet-another-react-lightbox": "^3.17.2"}, "devDependencies": {"@babel/preset-typescript": "^7.24.1", "@divinci-ai/server-utils": "file:../../resources/server-utils", "@playwright/test": "^1.39.0", "@svgr/webpack": "^8.1.0", "@testing-library/jest-dom": "^6.4.5", "@testing-library/react": "^14.2.2", "@types/async": "^3.2.24", "@types/body": "^5.1.4", "@types/busboy": "^1.5.4", "@types/cors": "^2.8.14", "@types/d3": "^7.4.3", "@types/enzyme": "^3.10.18", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/mime-types": "^2.1.4", "@types/multer": "^1.4.11", "@types/node": "^22.5.2", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.0", "@types/react-speech-recognition": "^3.9.5", "@types/regenerator-runtime": "^0.13.5", "@types/stream-to-blob": "^2.0.0", "@types/trusted-types": "^2.0.7", "compression-webpack-plugin": "^11.1.0", "cross-env": "^7.0.3", "css-loader": "^6.10.0", "dotenv": "^16.4.5", "glob": "^10.4.1", "html-webpack-plugin": "^5.6.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "node-fetch": "^3.3.2", "node-polyfill-webpack-plugin": "^4.0.0", "process": "^0.11.10", "raw-loader": "^4.0.2", "rimraf": "^6.0.1", "style-loader": "^3.3.4", "terser": "^5.30.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.1", "typescript": "^5.8.3", "webpack": "^5.96.1", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.1.0", "vitest": "^3.1.1", "@vitest/coverage-istanbul": "^3.1.1", "@vitest/ui": "^3.1.1", "jsdom": "^24.0.0"}, "engines": {"node": ">=20", "pnpm": ">=10"}, "packageManager": "pnpm@10.5.2+sha512.da9dc28cd3ff40d0592188235ab25d3202add8a207afbedc682220e4a0029ffbff4562102b9e6e46b4e3f9e8bd53e6d05de48544b0c57d4b0179e22c76d1199b"}