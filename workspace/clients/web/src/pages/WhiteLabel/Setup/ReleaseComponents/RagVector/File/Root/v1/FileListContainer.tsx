import React, { useState } from "react";
import { RagVectorFileListDisplay } from "./FileListDisplay";
import { RagVectorList } from "../../../RagVector/Root/List";
import ConfirmDeleteModal from "../../../../../../../../components/inputs/ConfirmDelete";
import { useTrackedValue } from "../../../../../../../../util/react/tracked-value";
import { JSON_EXTRA_Unknown, logDebug } from "@divinci-ai/utils";
import { showNotificationTemporarily } from "../../../../../Notifications/notificationUtils";

import { RagVectorFileDoc, RagVectorDoc, WhiteLabel } from "@divinci-ai/models";
import { useAuth0FetchJSON } from "../../../../../../../../globals/auth0-user";
import styles from "./rag-file.module.css";

export function FileListContainer({ whitelabel }: { whitelabel: WhiteLabel }){
  const [showNotification, setShowNotification] = useState(false);
  const [notificationMessage, setNotificationMessage] = useState("");
  // 📓 Setting _files in future from selecting a RAG:
  const [_files, setFiles] = useState<RagVectorFileDoc[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const auth0FetchJSON = useAuth0FetchJSON();

  const fetchFilesForRagVector = async (ragId: string)=>{
    try {
      // Fetch the RAG vector document
      const response = (await auth0FetchJSON(`/white-label/${whitelabel._id}/rag-vector/${ragId}`)) as RagVectorDoc;

      // Fetch full file details for each file pointer
      const files: RagVectorFileDoc[] = await Promise.all(
        response.files.map(async (filePointer)=>{
          const fullFileDetails = await auth0FetchJSON(`/white-label/${whitelabel._id}/rag-vector/files/${filePointer.fileId}`);
          return fullFileDetails as RagVectorFileDoc;
        })
      );

      setFiles(files);
    }catch(error){
      console.error("Error fetching files for RAG Vector:", error);
      setFiles([]);
    }
  };

  const { value: ragVectors } = useTrackedValue<Array<RagVectorDoc>>({
    url: `/white-label/${whitelabel._id}/rag-vector`,
  });

  const { loading, error: trackedError, value: files, update } = useTrackedValue<Array<RagVectorFileDoc>>({
    url: `/white-label/${whitelabel._id}/rag-vector/files`,
    eraseLastValue: false,
  });

  const handleSelectRagVector = (ragId: string)=>{
    fetchFilesForRagVector(ragId); // Trigger file fetch for the selected RAG vector
  };

  const updateFile = async (file: RagVectorFileDoc)=>{
    try {
      const response = await auth0FetchJSON(`/white-label/${whitelabel._id}/rag-vector/files/${file._id}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(file),
      });
      logDebug("🟢 File updated successfully.", response);
      update();
    }catch(error: any) {
      logDebug("❌ Error updating file: ", error);
    }
  };

  const deleteAllItems = async ()=>{
    try {
      const fileIds = (files || []).map((file) => file._id);
      const response = await auth0FetchJSON(`/white-label/${whitelabel._id}/rag-vector/files`, {
        method: "DELETE",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ fileIds }),
      });
      logDebug("🟢 All Files deleted successfully.", response);
      update();
    }catch(error) {
      logDebug("❌ Error deleting files: ", error);
    }
  };

  const handleConfirmDelete = async ()=>{
    await deleteAllItems();
    setIsModalOpen(false);
  };

  async function deleteFile(item: RagVectorFileDoc){
    try {
      const response = (await auth0FetchJSON(`/white-label/${whitelabel._id}/rag-vector/files/${item._id}`, {
        method: "DELETE",
      })) as JSON_EXTRA_Unknown;

      if((response as any)["status"] !== "error") {
        logDebug(`🟢 File: ${item.title} deleted successfully. \n Response: \n`, response);
        update();

        showNotificationTemporarily(
          setShowNotification,
          setNotificationMessage,
          `🟢 File: ${item.title} deleted successfully.`
        );
      }
    }catch(error) {
      logDebug("❌ Error deleting file: ", error);
      showNotificationTemporarily(
        setShowNotification,
        setNotificationMessage,
        `❌ Cannot delete ${item.title} while it is actively being used.`
      );
    }
  }

  async function restartFile(item: RagVectorFileDoc){
    try {
      const response = await auth0FetchJSON(`/white-label/${whitelabel._id}/rag-vector/files/${item._id}/restart`, {});
      logDebug(`🟢 File: ${item.title} restarted successfully! Response: ${response}`);
      showNotificationTemporarily(
        setShowNotification,
        setNotificationMessage,
        `🟢 File: ${item.title} restarted successfull!`
      );
      update();
    } catch (error) {
      logDebug(`❌ Error restarting file: ${item.title} \n ❌ Error: \n`, error);
      showNotificationTemporarily(
        setShowNotification,
        setNotificationMessage,
        `❌ File: ${item.title} failed to restart.`
      );
    }
  }

  return (
    <div className={styles.fileListContainer}>
      <RagVectorList />
      {showNotification && (
        <div className={`notification is-info ${styles.notification}`}>
          {notificationMessage}
        </div>
      )}
      <RagVectorFileListDisplay
        vectorlist={files || []}
        ragVectors={ragVectors || []}
        restartItem={restartFile}
        deleteItem={deleteFile}
        deleteAllItems={deleteAllItems}
        updateFiles={update}
        updateFile={updateFile}
        whitelabelTitle={whitelabel.title}
        whitelabelId={whitelabel._id}
      />
      <ConfirmDeleteModal
        resourceName={whitelabel.title}
        isOpen={isModalOpen}
        onConfirm={handleConfirmDelete}
        onCancel={() => setIsModalOpen(false)}
      />
    </div>
  );
}
