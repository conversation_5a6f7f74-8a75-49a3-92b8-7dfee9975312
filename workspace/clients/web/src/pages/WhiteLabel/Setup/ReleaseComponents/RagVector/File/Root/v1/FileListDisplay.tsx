import React, { useMemo, useState, useRef } from "react";
import { AgGridReact } from "@ag-grid-community/react";
import { ColDef, ModuleRegistry, GridReadyEvent } from "@ag-grid-community/core";
import { ClientSideRowModelModule } from "@ag-grid-community/client-side-row-model";
import { Link } from "react-router-dom";
import { replaceParams } from "../../../../../../../../util/router";
import { PATH_WHITELABEL_RAG_VECTOR_FILE_ITEM } from "../../../paths";
import "./file-list-display.css";
import { RagVectorFileDoc, RagVectorTextChunksStatus } from "@divinci-ai/models";
import ConfirmDeleteModal from "../../../../../../../../components/inputs/ConfirmDelete";

// Register AG-Grid modules
ModuleRegistry.registerModules([ClientSideRowModelModule]);

const getEnumKeyByValue = (enumObject: any, value: string) => {
  return Object.keys(enumObject).find((key) => enumObject[key] === value);
};

export function RagVectorFileListDisplay({
  vectorlist,
  restartItem,
  deleteItem,
  deleteAllItems,
  updateFiles,
  updateFile,
  whitelabelTitle,
  whitelabelId,
  ragVectors,
}: {
  vectorlist: Array<RagVectorFileDoc>;
  restartItem: (file: RagVectorFileDoc) => Promise<any>;
  deleteItem: (file: RagVectorFileDoc) => any;
  deleteAllItems: () => void;
  updateFiles: () => void;
  updateFile: (file: RagVectorFileDoc) => Promise<any>;
  whitelabelTitle: string;
  whitelabelId: string;
  ragVectors: Array<{ _id: string; title: string }>;
}) {
  const [selectedRows, setSelectedRows] = useState<RagVectorFileDoc[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const gridRef = useRef<AgGridReact<RagVectorFileDoc>>(null);

  const FILE_COLUMNS: ColDef<RagVectorFileDoc>[] = useMemo(() => {
    return [
      {
        headerName: "",
        width: 20,
        cellRenderer: "agGroupCellRenderer",
        checkboxSelection: true,
      },
      {
        headerName: "Title",
        field: "title",
        flex: 2,
        editable: true,
      },
      { headerName: "File Name", field: "originalFilename", flex: 1 },
      {
        headerName: "Upload Date",
        field: "uploadTimestamp",
        valueFormatter: (params: any) => {
          const date = new Date(params.value);
          return date.toLocaleDateString("en-US");
        },
        flex: 1,
      },
      { headerName: "Original Value", field: "rawFileKey", flex: 1 },
      {
        headerName: "Description",
        field: "description",
        editable: true,
        flex: 1,
      },
      {
        headerName: "Status",
        field: "status",
        cellRenderer: (params: any) => {
          const status = params.value;
          const loading = (function(){
            if(!status) return true;
            switch(status){
              case RagVectorTextChunksStatus.CHUNKING:
              case RagVectorTextChunksStatus.STORING:
                return true;
              default:
                return false;
            }
          })();

          return (
            <div>
              {loading && <span className="spinner" style={{ marginRight: "8px" }}></span>}
              <span>{status || "Unknown Status"}</span>
            </div>
          );
        },
        flex: 1,
      },
      {
        headerName: "Used By",
        cellRenderer: (params: any) => {
          const ragVectors = params.data.ragVectors;
          return !ragVectors ? 0 : ragVectors.length;
        },
        flex: 1
      },
      {
        headerName: "Actions",
        cellRenderer: (params: any) => {
          return (
            <div>
              <Link
                className="button is-small"
                to={replaceParams(PATH_WHITELABEL_RAG_VECTOR_FILE_ITEM, {
                  ...params,
                  fileId: params.data._id,
                  whitelabelId: whitelabelId,
                })}
              >
                Chunks
              </Link>
            </div>
          );
        },
        flex: 1,
      },
    ];
  }, [ragVectors, whitelabelId]);

  const onFilterByRagVector = (vectorId: string) => {
    const api = gridRef.current?.api;
    if (api) {
      api.setFilterModel({
        ragVectorId: {
          filterType: "text",
          type: "equals",
          filter: vectorId,
        },
      });
      api.onFilterChanged();
    }
  };

  const clearRagVectorFilter = () => {
    const api = gridRef.current?.api;
    if (api) {
      api.setFilterModel(null);
      api.onFilterChanged();
    }
  };

  const onSelectionChanged = (event: any) => {
    setSelectedRows(event.api.getSelectedRows());
  };

  return (
    <>
      {selectedRows.length > 0 && (
        <div className="buttons mt-4">
          <button className="button is-primary" onClick={() => selectedRows.forEach(restartItem)}>
            🔄 Restart Selected
          </button>
          <button className="button is-danger" onClick={() => selectedRows.forEach(deleteItem)}>
            🚮 Delete Selected
          </button>
        </div>
      )}
      {vectorlist && vectorlist.length > 0 ? (
        <>
          {/*
            // 📓 Will add this back later:
            // 🎟️ AS-146 https://divinci.atlassian.net/browse/AS-146
            <RagVectorFilterButtons
            vectors={ragVectors}
            onFilterByRagVector={onFilterByRagVector}
            clearRagVectorFilter={clearRagVectorFilter}
          /> */}
          <div className="ag-theme-alpine-auto-dark" style={{ height: "500px", width: "100%" }}>
            <AgGridReact
              ref={gridRef}
              columnDefs={FILE_COLUMNS}
              rowData={vectorlist}
              rowSelection={"multiple"}
              enableCellTextSelection={true}
              onSelectionChanged={onSelectionChanged}
              onCellValueChanged={async ({ data }) => {
                await updateFile(data);
              }}
              domLayout="autoHeight"
              context={{ whitelabelId }}
            />
          </div>
        </>
      ) : (
        <div className="notification is-info mt-4">No files to display. Upload some with the <span className="tag">Upload file(s) ⌄</span> button above.</div>
      )}
      <ConfirmDeleteModal
        resourceName={whitelabelTitle}
        isOpen={isModalOpen}
        onConfirm={() => {
          deleteAllItems();
          setIsModalOpen(false);
        }}
        onCancel={() => setIsModalOpen(false)}
      />
    </>
  );
}
