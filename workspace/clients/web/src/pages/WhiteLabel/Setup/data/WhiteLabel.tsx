import React, {
  useState,
  createContext,
  useContext,
  useEffect,
  PropsWithChildren,
  useRef,
} from "react";
import { useParams } from "react-router";
import { useAuth0 } from "@auth0/auth0-react";
import {
  useAuth0FetchJSON,
} from "../../../../globals/auth0-user";

import { WhiteLabel } from "@divinci-ai/models";

type WhiteLabelContextType = {
  id: string,
  whitelabel: null | WhiteLabel,
  update: ()=>any,
};

const WhiteLabelContext = createContext<WhiteLabelContextType>({
  id: "",
  whitelabel: null,
  update: ()=>{},
});

export function useWhiteLabel(){
  const context = useContext(WhiteLabelContext);
  return context;
}

export function WhiteLabelProvider({ children }: PropsWithChildren){
  const { isAuthenticated } = useAuth0();
  const auth0FetchJSON = useAuth0FetchJSON();
  const { whitelabelId } = useParams();

  const [whitelabel, setWhiteLabel] = useState<null | WhiteLabel>(null);

  const [forceUpdate, setForceUpdate] = useState(Date.now());
  const [error, setError] = useState<string>("");

  const lastId = useRef<string>("0");

  useEffect(()=>{
    if(!isAuthenticated){
      return;
    }
    if(typeof whitelabelId !== "string") {
      setWhiteLabel(null);
      return;
    }
    lastId.current = whitelabelId;
    setWhiteLabel(null);

    const fetcher = auth0FetchJSON;
    fetcher(
      `/white-label/${whitelabelId}`,
    ).then(async (data)=>{
      if(lastId.current !== whitelabelId) return;
      const { whitelabel } = data as any as { whitelabel: WhiteLabel };
      setWhiteLabel(whitelabel);

    })
    .catch((e)=>{
      setError(e.message);
    });

    return ()=>{
      lastId.current = "0";
    };
  }, [whitelabelId, auth0FetchJSON, forceUpdate]);

  if(error) {
    console.error("whitelabel retrieve error:", error);
    return <h1>Unable to Retrieve Whitelabel</h1>;
  }

  return (
    <WhiteLabelContext.Provider
      value={{
        id: whitelabelId || "",
        whitelabel: whitelabel,
        update: update,
      }}
    >
      {children}
    </WhiteLabelContext.Provider>
  );

  function update(){
    setForceUpdate(Date.now());
    console.log("🙋🏻 Should update whitelabel.");
  }
}
