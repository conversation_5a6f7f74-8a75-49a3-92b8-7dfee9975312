import {
  IDocPermissionModel, IDocPermissionMethods as IMethods
} from "../model/types";
import { UserGroupModel } from "../../UserGroup";
import { HTTP_ERRORS_WITH_CONTEXT } from "@divinci-ai/server-utils";

export const getUsersPermissions: IMethods["getUsersPermissions"] = async function(
  this: InstanceType<IDocPermissionModel>,
  config, { userId, activeUserOrg }
){
  // The owner should have all permissions
  if(this.ownerUser === userId){
    return config.allValidPermissions;
  }

  if(typeof userId === "undefined"){
    return this.anonymousPermissions.permissions;
  }

  const specifiedPermissions = this.userPermissions.get(userId);
  if(typeof specifiedPermissions !== "undefined"){
    return specifiedPermissions.permissions;
  }

  if(activeUserOrg){
    const permissionList = await handleGroupPermission(this, userId, activeUserOrg);
    if(permissionList !== null) return permissionList;
  }

  return this.loggedInPermissions.permissions;
};

import { Types } from "mongoose";
async function handleGroupPermission(
  permission: InstanceType<IDocPermissionModel>,
  userId: string, groupId: string,
){
  const group = await UserGroupModel.findOne({
    _id: new Types.ObjectId(groupId),
    "users.userId": userId
  }).select(["_id", "slug"]);
  if(!group){
    throw HTTP_ERRORS_WITH_CONTEXT.FORBIDDEN("No access to organization");
  }
  const groupPermissions = permission.group.get(group.slug);
  if(!groupPermissions){
    return null;
  }
  return groupPermissions.permissions;
}

export const isUserAllowed: IMethods["isUserAllowed"] = async function(
  this: InstanceType<IDocPermissionModel>, config, user, permission
){
  const permissions = await this.getUsersPermissions(config, user);
  return permissions.includes(permission);
};
