import { GPTTokens, supportModelType } from "gpt-tokens";
import { <PERSON><PERSON><PERSON> } from "buffer";
import {
  getOpenAI,
  getTwilio,
  TWILIO_PHONE_NUMBER,
  getPublicTranscriptFileR2Instance,
} from "@divinci-ai/server-globals";
import { HTTP_ERRORS } from "@divinci-ai/server-utils";

import { AI_CATEGORY_ENUM } from "@divinci-ai/models";
import { AIAssistantAPI } from "../../types";

const { openai } = getOpenAI();
const { twilioClient } = getTwilio();

type RawOpenAIImages = Awaited<
  ReturnType<(typeof openai)["images"]["generate"]>
>["data"];

interface AssistantInfo {
  title: string,
  url: string,
  type: string,
  version: string,
  description: string,
  deprecated: boolean,
}

interface AssistantModel {
  model: string,
  inputPrice: bigint,
}

export function buildAssistant(
  info: AssistantInfo,
  { model, inputPrice }: AssistantModel
): AIAssistantAPI<{ prompt: string, user: string }, RawOpenAIImages>{
  return {
    info: {
      ...info,
      apiName: "openai",
      assistantName: model,
      category: AI_CATEGORY_ENUM.IMAGE,
      canReply: false,

      org: "openai",
      orgUrl: "https://openai.com",
    },
    useContext: false,
    async countTokens(text: string): Promise<number>{
      return countTokens([{ role: "user", content: text }]);
    },
    calculateInputValue(): bigint{
      return inputPrice;
    },
    calculateOutputValue(): bigint{
      return BigInt(0);
    },
    generateValue: async (thread, { id: responseId })=>{
      const item = thread.pop();
      if(typeof item === "undefined") {
        throw new Error("Cannot get image with no thread");
      }

      // Validate and sanitize the prompt
      if(!item.prompt || typeof item.prompt !== 'string') {
        throw new Error("Invalid prompt for image generation");
      }

      // Trim and limit prompt length (OpenAI has a 4000 character limit for DALL-E)
      const sanitizedPrompt = item.prompt.trim().substring(0, 4000);

      if(sanitizedPrompt.length === 0) {
        throw new Error("Empty prompt for image generation");
      }

      // Validate user parameter
      if(!item.user || typeof item.user !== 'string') {
        throw new Error("Invalid user identifier for image generation");
      }

      console.log(`🎨 Generating image with DALL-E ${model.replace('dall-e-', '')} for user: ${item.user}`);
      console.log(`🎨 Prompt: ${sanitizedPrompt.substring(0, 100)}${sanitizedPrompt.length > 100 ? '...' : ''}`);

      const response = await openai.images.generate({
        model: model,
        prompt: sanitizedPrompt,
        response_format: "b64_json",
        user: item.user,
      });

      const raw = response.data;
      if(!raw || raw.length === 0) {
        throw HTTP_ERRORS.SERVER_ERROR;
      }
      const responseData = raw[0].b64_json;
      if(typeof responseData !== "string") throw HTTP_ERRORS.SERVER_ERROR;

      const { uploadFile } = getPublicTranscriptFileR2Instance();

      const responseURL = await uploadFile(
        responseId + ".png",
        Buffer.from(responseData, "base64"),
      );

      return {
        raw: raw,
        text: responseURL,
      };
    },
    sendSMSMessage(phoneNumber, response){
      return twilioClient.messages.create({
        from: TWILIO_PHONE_NUMBER,
        to: phoneNumber,
        mediaUrl: [response.text],
      });
    },
    createSystemMessage(){
      return null;
    },
    contextToInternal(){
      return [];
    },
    chatMessageToInternal(chatmessage){
      // Sanitize the user ID for OpenAI API
      // OpenAI expects a clean identifier without special characters
      const sanitizedUserId = chatmessage.name
        ? chatmessage.name.replace(/[^a-zA-Z0-9-_]/g, '_').substring(0, 64)
        : 'anonymous_user';

      return {
        prompt: chatmessage.content,
        user: sanitizedUserId,
      };
    },
  };
}

function countTokens(messages: GPTTokens["messages"], model = "gpt-4o-mini"): number{
  const value = new GPTTokens({ messages, model: model as supportModelType });
  return value.usedTokens;
}
