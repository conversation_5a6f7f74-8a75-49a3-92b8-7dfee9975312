{"name": "@divinci-ai/public-api", "version": "0.3.0", "private": true, "description": "Server for <PERSON><PERSON><PERSON>", "main": "dist/index.js", "typings": "src/index.ts", "scripts": {"start": "node dist/index.js", "bundle": "rimraf ./bundle/bundle.js &&  node ./bundle/esbuild.config.js ./ ./bundle/bundle.js", "build": "tsc", "build:ci": "tsc --skipL<PERSON><PERSON><PERSON><PERSON> --project tsconfig.ci.json", "build:ignore-errors": "rimraf ./dist && tsc --skipLib<PERSON>heck --noEmit || echo 'ℹ️ TypeScript errors ignored'", "build:ignore-errors:ci": "tsc --skipLib<PERSON>heck --noEmit --project tsconfig.ci.json || echo 'ℹ️ TypeScript errors ignored'", "test:build": "tsc --project tsconfig.tests.json", "prepare": "rimraf ./dist && tsc", "start:dev": "ts-node-dev --poll --transpile-only --ignore-watch node_modules --files src/index.ts", "bundle:build:clean": "rimraf ./dist && mkdir -p ./dist", "bundle:build:js": "node ./bundle/esbuild.config.js ./ ./dist/", "bundle:build": "npm run bundle:build:clean && npm run bundle:build:js", "bundle:start": "env $(node dist/env.bundle.js) node dist/app.bundle.js", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage"}, "author": "", "license": "JSON", "dependencies": {"@aws-sdk/client-s3": "^3.750.0", "@aws-sdk/s3-presigned-post": "^3.772.0", "@aws-sdk/s3-request-presigner": "^3.787.0", "@divinci-ai/models": "file:../../resources/models", "@divinci-ai/server-globals": "file:../../resources/server-globals", "@divinci-ai/server-models": "file:../../resources/server-models", "@divinci-ai/server-permissions": "file:../../resources/server-permissions", "@divinci-ai/server-tools": "file:../../resources/server-tools", "@divinci-ai/server-utils": "file:../../resources/server-utils", "@divinci-ai/tools": "file:../../resources/tools", "@divinci-ai/utils": "file:../../resources/utils", "busboy": "^1.6.0", "cors": "^2.8.5", "emoji-regex": "^10.3.0", "express": "^4.19.2", "get-stream": "^8.0.1", "mime-types": "^2.1.35", "mongoose": "^8.8.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-fetch-commonjs": "^3.3.2", "stream-to-blob": "^2.0.1", "tmp": "^0.2.3"}, "devDependencies": {"@types/async": "^3.2.24", "@types/body": "^5.1.4", "@types/busboy": "^1.5.4", "@types/cors": "^2.8.13", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/mime-types": "^2.1.4", "@types/morgan": "^1.9.5", "@types/multer": "^1.4.11", "@types/node": "^22.5.2", "@types/stream-to-blob": "^2.0.0", "@types/tmp": "^0.2.6", "@vitest/coverage-istanbul": "^3.1.1", "@vitest/ui": "^3.1.1", "dotenv": "^16.4.5", "esbuild": "^0.23.1", "glob": "^10.4.1", "rimraf": "^6.0.1", "supertest": "^6.3.3", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3", "vite-tsconfig-paths": "5.0.0", "vitest": "^3.1.1"}, "engines": {"node": ">=20", "pnpm": ">=10"}, "packageManager": "pnpm@10.5.2+sha512.da9dc28cd3ff40d0592188235ab25d3202add8a207afbedc682220e4a0029ffbff4562102b9e6e46b4e3f9e8bd53e6d05de48544b0c57d4b0179e22c76d1199b"}