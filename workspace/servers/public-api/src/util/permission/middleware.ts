import { HTTP_ERRORS } from "@divinci-ai/server-utils";
import { Request<PERSON><PERSON><PERSON> } from "express";
import { IPermissionGetterConfig } from "@divinci-ai/server-permissions";
import { getUserId, getUsersActiveGroup } from "@divinci-ai/server-globals";
import { DocPermissionModel } from "@divinci-ai/server-models";
import { DIVINCI_TEST_ENVIRONMENT, DIVINCI_PERMISSION_DRY_HEADER } from "@divinci-ai/server-globals";


export function prepareMiddleware(config: IPermissionGetterConfig){
  return (permissionType: string)=>{
    return middleware(config, permissionType);
  };
}

export function middleware(config: IPermissionGetterConfig, permission: string): RequestHandler{
  if(!config.verbosePermissions.find(({ key })=>(key === permission))){
    throw new Error(`❌ permission ${permission} missing from config ${config.name}`);
  }
  return async (req, res, next)=>{
    try {
      const userId = getUserId(req);
      const activeUserOrg = getUsersActiveGroup(req);
      const doc = await config.getDocument(req.params);
      if(doc === null) throw HTTP_ERRORS.NOT_FOUND;

      const permissionDoc = await DocPermissionModel.findOrCreatePermission(doc, config);
      if(!await permissionDoc.isUserAllowed(
        { allValidPermissions: config.defaultValues.allValidPermissions },
        { userId, activeUserOrg },
        permission
      )){
        throw HTTP_ERRORS.FORBIDDEN;
      }
      if(!DIVINCI_TEST_ENVIRONMENT) return next();

      const overrideHeader = req.headers[DIVINCI_PERMISSION_DRY_HEADER];
      if(overrideHeader === "true"){
        return res.status(200).send({ status: "permission-override", });
      }
      next();
    }catch(e){
      next(e);
    }
  };
}
